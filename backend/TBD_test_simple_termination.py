#!/usr/bin/env python3
"""
Simple test to confirm automatic deployment termination when balance goes negative
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import psycopg2
from datetime import datetime, timezone, timedelta
from app.services.billing_job import run_billing_cycle

# Database connection details
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'manidae',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def get_user_balance(user_id=1):
    """Get current user balance"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT balance FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            return float(result[0]) if result else 0.0
    finally:
        conn.close()

def set_user_balance(user_id, balance):
    """Set user balance to specific amount"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("UPDATE users SET balance = %s WHERE id = %s", (balance, user_id))
            conn.commit()
            print(f"✓ Set user {user_id} balance to €{balance:.2f}")
    finally:
        conn.close()

def reset_deployment_billing(deployment_id):
    """Reset deployment billing to force new billing"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            # Set last_billed_at to yesterday to force billing for today
            yesterday = datetime.now(timezone.utc) - timedelta(days=1)
            yesterday_end = yesterday.replace(hour=23, minute=59, second=59)
            
            cur.execute("""
                UPDATE deployments 
                SET last_billed_at = %s 
                WHERE id = %s
            """, (yesterday_end, deployment_id))
            conn.commit()
            print(f"✓ Reset deployment {deployment_id} billing to force new charge")
    finally:
        conn.close()

def test_simple_termination():
    """Test simple automatic termination"""
    
    print("🚨 Testing Simple Automatic Deployment Termination")
    print("=" * 50)
    
    user_id = 1
    deployment_id = 5
    
    # Step 1: Check current state
    print("\n📋 Step 1: Check current state")
    current_balance = get_user_balance(user_id)
    print(f"Current user balance: €{current_balance:.2f}")
    
    # Step 2: Set balance to insufficient amount
    print("\n📋 Step 2: Set balance to insufficient amount")
    insufficient_balance = 0.50  # Less than €1.92 daily cost
    set_user_balance(user_id, insufficient_balance)
    
    # Step 3: Reset deployment billing to force charge
    print("\n📋 Step 3: Reset deployment billing to force charge")
    reset_deployment_billing(deployment_id)
    
    # Step 4: Run billing cycle
    print("\n📋 Step 4: Run billing cycle")
    print("This should charge €1.92, making balance negative and triggering termination...")
    
    result = run_billing_cycle()
    print(f"Billing result: {result}")
    
    # Step 5: Check final state
    print("\n📋 Step 5: Check final state")
    final_balance = get_user_balance(user_id)
    print(f"Final user balance: €{final_balance:.2f}")
    
    # Step 6: Conclusion
    print("\n🎯 CONCLUSION:")
    if result['charged_users'] > 0 and result['terminated_deployments'] > 0 and final_balance < 0:
        print("✅ AUTOMATIC TERMINATION WORKS CORRECTLY!")
        print(f"   - User was charged: {result['charged_users']} user(s)")
        print(f"   - Balance went negative: €{final_balance:.2f}")
        print(f"   - Deployments terminated: {result['terminated_deployments']}")
    else:
        print("❌ AUTOMATIC TERMINATION DID NOT WORK AS EXPECTED!")
        print(f"   - Charged users: {result['charged_users']}")
        print(f"   - Terminated deployments: {result['terminated_deployments']}")
        print(f"   - Final balance: €{final_balance:.2f}")

if __name__ == "__main__":
    test_simple_termination()
