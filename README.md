# Manidae Cloud

A multi-cloud deployment platform that simplifies deploying containerized applications across multiple cloud providers using Infrastructure as Code.

## Overview

Manidae Cloud is a Platform-as-a-Service (PaaS) solution that allows users to deploy containerized applications with automated infrastructure management, cost optimization, and multi-cloud support. The platform provides a user-friendly interface for deploying applications while handling the complexity of cloud infrastructure provisioning behind the scenes.

## Features

### 🌐 Multi-Cloud Support
- **Google Cloud Platform (GCP)**
- **Hetzner Cloud**
- **Linode**
- **Vultr**

### 📦 Application Packages
- **Pangolin**: Basic deployment package with essential features
- **Premium**: Advanced package with OAuth authentication, CrowdSec security, and additional enterprise features

### 🛠️ Infrastructure as Code
- Automated Terraform-based infrastructure provisioning
- Custom Komodo provider for container orchestration
- Provider-specific configuration generation
- Infrastructure lifecycle management (create, update, destroy)

### 💰 Dynamic Pricing
- Real-time cost calculation based on:
  - Cloud provider selection
  - Instance type and region
  - Application package (Pangolin/Premium)
  - Support level (Level 1, 2, or 3)

### 🎯 Support Tiers
- **Level 1**: Basic support with 7-day backup retention, email support, 3-day response time
- **Level 2**: Enhanced support with 14-day backup retention, 24h response time, priority queuing
- **Level 3**: Premium support with 30-day backup retention, 4h response time, dedicated customer success manager

### 🔐 Security & Authentication
- JWT-based user authentication
- API key verification for secure endpoints
- Rate limiting middleware
- User-based deployment ownership and access control

## Architecture

### Backend (Python/FastAPI)
- **API Framework**: FastAPI with automatic OpenAPI documentation
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT tokens with secure user management
- **Cloud Providers**: Strategy pattern for extensible multi-cloud support
- **Infrastructure**: Terraform integration with custom providers

### Frontend (React/TypeScript)
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS for responsive design
- **State Management**: React Context API
- **Routing**: React Router for SPA navigation
- **UI/UX**: Multi-step deployment wizard with real-time pricing

### Infrastructure
- **Terraform**: Infrastructure as Code with provider-specific templates
- **Komodo Provider**: Custom Terraform provider for container management
- **Jinja2 Templates**: Dynamic configuration file generation
- **Multi-Cloud**: Abstracted deployment across different cloud providers

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- Docker and Docker Compose
- Terraform
- PostgreSQL

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd manidae-cloud
   ```

2. **Set up the backend environment**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the database**
   ```bash
   docker-compose up -d db
   ```

5. **Initialize the database**
   ```bash
   python -m app.db.create_tables
   python -m app.db.init_db
   python -m app.db.load_pricing_data
   ```

6. **Start the backend server**
   ```bash
   uvicorn app.main:app --reload
   ```

7. **Run a billing run**
   ```bash
   python -m app.services.billing_job
   ```

8. ** Check a billing run for a user**
   ```bash
   python -m app.services.check_billing_status
   ```

 Usage:
  - python -m app.services.check_billing_status - Uses default user ID 1
  - python -m app.services.check_billing_status 5 - Uses user ID 5
  - python -m app.services.check_billing_status 123 - Uses user ID 123

### Frontend Setup

1. **Install dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

### Terraform Komodo Provider Setup

1. **Build the custom provider**
   ```bash
   cd submodules/terraform-provider-komodo
   go mod tidy
   go build -o ./bin/terraform-provider-komodo-provider
   ```

2. **Configure Terraform**
   ```bash
   vi ~/.terraformrc
   ```
   Add:
   ```hcl
   provider_installation {
     dev_overrides {
       "example.com/me/komodo-provider" = "/path/to/manidae-cloud/submodules/terraform-provider-komodo/bin/"
     }
     direct {}
   }
   ```

## API Documentation

Once the backend is running, visit:
- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

### Key Endpoints

- `POST /api/deployments/` - Create a new deployment
- `GET /api/deployments/` - List user deployments
- `DELETE /api/deployments/{id}` - Delete a deployment
- `GET /api/deployments/cloud-providers/` - Get available cloud providers
- `GET /api/deployments/regions/{provider}` - Get regions for a provider
- `POST /api/deployments/pricing/calculate` - Calculate deployment pricing

## Development

### Adding New Cloud Providers

1. **Create a new provider strategy**
   ```bash
   # Create new file: backend/app/core/deployment/providers/aws.py
   ```

2. **Implement the ProviderStrategy interface**
   ```python
   from app.core.deployment.providers.base import ProviderStrategy
   
   class AwsStrategy(ProviderStrategy):
       def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel):
           # Implementation here
           pass
   ```

3. **Register the provider**
   ```python
   # In backend/app/core/deployment/deployment_manager.py
   self.provider_strategies = {
       # ... existing providers
       "AWS": AwsStrategy(),
   }
   ```

### Database Management

- **Reset database**: `python -m app.db.init_db`
- **Load pricing data**: `python -m app.db.load_pricing_data`
- **Database migrations**: Use Alembic for schema changes

### Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

## Troubleshooting & Admin Scripts

In the event of a user billing complaint or other data inconsistency, two helper scripts are available in `backend/app/services/` to diagnose and correct issues.

**Important:** To run these scripts, navigate to the `backend` directory and prepend the command with `PYTHONPATH=.` to ensure the application modules are correctly loaded.

### Checking User Billing Status

The `check_billing_status.py` script is a safe, read-only tool to get a quick overview of a user's billing state. It is the recommended first step for investigating any billing issue.

**What it does:**
- Displays the user's current balance.
- Shows the last billing timestamp for their active deployments.
- Prints the details of their most recent billing transaction.

**Usage:**
```bash
cd backend
PYTHONPATH=. python app/services/check_billing_status.py
```
*(Note: The script is hard-coded for user ID 1. You can edit the script to change the user ID.)*

### Correcting User Billing Data

The `fix_billing.py` script is a powerful tool to completely reset a user's billing history. It should be used with caution when a user has been significantly over- or under-charged due to an error.

**What it does:**
- Deletes all previous "billing" transactions for the user.
- Reverts the user's balance to what it was before those transactions occurred.
- Resets the billing timestamp on all their active deployments, allowing the main billing job to run again from scratch for that user.

**Usage:**
```bash
cd backend
PYTHONPATH=. python app/services/fix_billing.py
```

**⚠️ Warning:** This is a destructive action. It permanently deletes billing history for the user. Only use it when you are sure a complete reset is necessary.

## Configuration

### Environment Variables

**Backend (.env)**:
```env
DATABASE_URL=postgresql://user:password@localhost:5432/manidae_cloud
GCP_PROJECT_ID=your-gcp-project
KOMODO_PROVIDER_ENDPOINT=https://your-komodo-endpoint
KOMODO_API_KEY=your-api-key
KOMODO_API_SECRET=your-api-secret
GITHUB_TOKEN=your-github-token
SSH_PUBLIC_KEY=your-ssh-public-key
HCLOUD_TOKEN=your-hetzner-token
```

**Frontend (.env)**:
```env
REACT_APP_API_URL=http://localhost:8000
```

## Deployment

### Production Deployment

1. **Backend**: Deploy using Docker, Kubernetes, or cloud services
2. **Frontend**: Build and serve static files
3. **Database**: Use managed PostgreSQL service
4. **Security**: Configure proper authentication, HTTPS, and API keys

### Security Considerations

- Use strong JWT secrets in production
- Configure CORS properly for your domain
- Use environment variables for sensitive data
- Implement proper API rate limiting
- Use HTTPS in production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]

## Support

For support and questions:
- Create an issue in the repository
- Check the API documentation
- Review the backend and frontend README files for detailed information

## Roadmap

- [ ] AWS provider support
- [ ] Azure provider support
- [ ] Enhanced monitoring and logging
- [ ] Automated backup management
- [ ] Cost optimization recommendations
- [ ] Multi-region deployments
- [ ] CI/CD pipeline integration