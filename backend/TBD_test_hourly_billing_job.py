#!/usr/bin/env python3
"""
Test billing job running every hour to demonstrate proper daily billing
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone, timedelta
from app.services.billing_job import run_billing_cycle

def test_hourly_billing_runs():
    """Test billing job running every hour for 48 hours"""
    
    print("🕐 Testing Billing Job Running Every Hour")
    print("=" * 50)
    print("This simulates a billing job running every hour for 48 hours")
    print("to demonstrate that deployments are only charged once per day.\n")
    
    # Start from current time
    start_time = datetime.now(timezone.utc)
    
    charges_by_day = {}
    total_runs = 0
    total_charges = 0
    
    # Run billing job every hour for 48 hours (2 days)
    for hour in range(48):
        current_time = start_time + timedelta(hours=hour)
        day_key = current_time.strftime('%Y-%m-%d')
        
        print(f"⏰ Hour {hour+1:2d}: {current_time.strftime('%Y-%m-%d %H:00')} UTC", end=" ")
        
        # Run billing cycle
        result = run_billing_cycle(current_time)
        total_runs += 1
        
        if result['charged_users'] > 0:
            total_charges += 1
            if day_key not in charges_by_day:
                charges_by_day[day_key] = []
            charges_by_day[day_key].append(hour + 1)
            print(f"💰 CHARGED {result['charged_users']} user(s)")
        else:
            print("⏭️  No charges")
        
        if result['terminated_deployments'] > 0:
            print(f"   🛑 Terminated {result['terminated_deployments']} deployment(s)")
            break
            
        if result['errors']:
            print(f"   ❌ Errors: {result['errors']}")
    
    print("\n" + "=" * 50)
    print("📊 BILLING SUMMARY")
    print("=" * 50)
    print(f"Total billing job runs: {total_runs}")
    print(f"Total times charges occurred: {total_charges}")
    print(f"Days with charges: {len(charges_by_day)}")
    
    print("\nCharges by day:")
    for day, hours in charges_by_day.items():
        print(f"  {day}: Charged at hour(s) {hours}")
        if len(hours) > 1:
            print(f"    ⚠️  WARNING: Multiple charges on same day!")
        else:
            print(f"    ✅ Correct: Only one charge per day")
    
    # Expected behavior: Should only charge once per day
    expected_charges = len(charges_by_day)
    if total_charges == expected_charges:
        print(f"\n✅ BILLING BEHAVIOR IS CORRECT!")
        print(f"   Expected: {expected_charges} charges (one per day)")
        print(f"   Actual: {total_charges} charges")
    else:
        print(f"\n❌ BILLING BEHAVIOR IS INCORRECT!")
        print(f"   Expected: {expected_charges} charges (one per day)")
        print(f"   Actual: {total_charges} charges")

if __name__ == "__main__":
    test_hourly_billing_runs()
