#!/usr/bin/env python3
"""
Test automatic deployment termination when user balance drops below zero
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import psycopg2
from datetime import datetime, timezone, timedelta
from app.services.billing_job import run_billing_cycle

# Database connection details
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'manidae',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def get_user_balance(user_id=1):
    """Get current user balance"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT balance FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            return float(result[0]) if result else 0.0
    finally:
        conn.close()

def set_user_balance(user_id, balance):
    """Set user balance to specific amount"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("UPDATE users SET balance = %s WHERE id = %s", (balance, user_id))
            conn.commit()
            print(f"✓ Set user {user_id} balance to €{balance:.2f}")
    finally:
        conn.close()

def get_active_deployments(user_id=1):
    """Get active deployments for user"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, status, cost, created_at, last_billed_at, deleted_at
                FROM deployments 
                WHERE user_id = %s AND status = 'ACTIVE' AND deleted_at IS NULL
            """, (user_id,))
            return cur.fetchall()
    finally:
        conn.close()

def get_recent_transactions(user_id=1, limit=10):
    """Get recent transactions for user"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, amount, type, description, created_at
                FROM transactions 
                WHERE user_id = %s 
                ORDER BY created_at DESC 
                LIMIT %s
            """, (user_id, limit))
            return cur.fetchall()
    finally:
        conn.close()

def test_insufficient_balance_termination():
    """Test automatic termination when balance goes negative"""
    
    print("🚨 Testing Automatic Deployment Termination on Insufficient Balance")
    print("=" * 70)
    
    user_id = 1
    
    # Step 1: Check initial state
    print("\n📋 Step 1: Check initial state")
    initial_balance = get_user_balance(user_id)
    active_deployments = get_active_deployments(user_id)
    
    print(f"Initial user balance: €{initial_balance:.2f}")
    print(f"Active deployments: {len(active_deployments)}")
    
    for dep in active_deployments:
        dep_id, status, cost, created_at, last_billed_at, deleted_at = dep
        daily_cost = float(cost) * 24 / 100  # Convert cents/hour to euros/day
        print(f"  Deployment {dep_id}: {status}, €{daily_cost:.2f}/day")
    
    if not active_deployments:
        print("❌ No active deployments found! Cannot test termination.")
        return
    
    # Step 2: Set balance to a low positive amount
    print("\n📋 Step 2: Set balance to low positive amount")
    low_balance = 1.50  # Less than daily cost of €1.92
    set_user_balance(user_id, low_balance)
    
    # Step 3: Trigger billing that will make balance negative
    print("\n📋 Step 3: Trigger billing to make balance negative")
    print("Simulating billing job that will charge €1.92...")
    
    # Simulate next day to trigger billing
    tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
    result = run_billing_cycle(tomorrow)
    
    print(f"Billing result: {result}")
    
    # Step 4: Check results
    print("\n📋 Step 4: Check results after billing")
    
    # Check new balance
    new_balance = get_user_balance(user_id)
    print(f"New user balance: €{new_balance:.2f}")
    
    # Check deployment status
    remaining_deployments = get_active_deployments(user_id)
    print(f"Remaining active deployments: {len(remaining_deployments)}")
    
    # Check recent transactions
    recent_transactions = get_recent_transactions(user_id, 5)
    print(f"\nRecent transactions:")
    for tx in recent_transactions:
        tx_id, amount, tx_type, description, created_at = tx
        print(f"  {created_at.strftime('%H:%M:%S')} | €{float(amount):6.2f} | {tx_type:12} | {description}")
    
    # Step 5: Analyze results
    print("\n📋 Step 5: Analysis")
    print("-" * 40)
    
    if result['terminated_deployments'] > 0:
        print(f"✅ SUCCESS: {result['terminated_deployments']} deployment(s) were automatically terminated!")
        print(f"   Balance went from €{low_balance:.2f} to €{new_balance:.2f}")
        print(f"   Termination triggered as expected when balance < €0.00")
    else:
        print(f"❌ FAILURE: No deployments were terminated!")
        print(f"   Balance: €{new_balance:.2f}")
        print(f"   This suggests the termination logic may not be working")
    
    if result['charged_users'] > 0:
        print(f"✅ Billing charge applied: {result['charged_users']} user(s) charged")
    else:
        print(f"⚠️  No billing charges applied")
    
    if result['errors']:
        print(f"❌ Errors occurred: {result['errors']}")
    else:
        print(f"✅ No errors during billing/termination process")
    
    # Step 6: Verify termination transaction
    print("\n📋 Step 6: Verify termination transaction")
    termination_transactions = [tx for tx in recent_transactions if tx[2] == 'termination']
    
    if termination_transactions:
        print("✅ Termination transaction found:")
        for tx in termination_transactions:
            tx_id, amount, tx_type, description, created_at = tx
            print(f"   {description} (€{float(amount):.2f})")
    else:
        print("❌ No termination transaction found")
    
    print(f"\n🎯 CONCLUSION:")
    if result['terminated_deployments'] > 0 and new_balance < 0:
        print("✅ AUTOMATIC TERMINATION WORKS CORRECTLY!")
        print("   - Balance went negative")
        print("   - Deployment(s) were automatically terminated")
        print("   - Termination transaction was recorded")
    else:
        print("❌ AUTOMATIC TERMINATION DID NOT WORK AS EXPECTED!")

if __name__ == "__main__":
    test_insufficient_balance_termination()
