import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth, useDeployment } from '../contexts';
import { Deployment } from '../api/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, ExternalLink } from 'lucide-react';
import Modal from '@/components/ui/modal';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const {
    deployments,
    isLoading,
    error,
    fetchDeployments,
    deleteDeployment,
    clearError,
  } = useDeployment();
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [detailOpen, setDetailOpen] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deploymentToDelete, setDeploymentToDelete] = useState<Deployment | null>(null);

  useEffect(() => {
    if (user) {
      // Let the backend handle role-based filtering
      fetchDeployments();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleDeleteDeployment = (deployment: Deployment) => {
    setDeploymentToDelete(deployment);
    setShowDeleteModal(true);
  };

  const confirmDeleteDeployment = async () => {
    if (!deploymentToDelete) return;

    setDeletingId(deploymentToDelete.id);
    try {
      await deleteDeployment(deploymentToDelete.id);
      setShowDeleteModal(false);
      setDeploymentToDelete(null);
    } catch (error) {
      // Error handled by context
    } finally {
      setDeletingId(null);
    }
  };

  const formatCost = (cost: number) => {
    // Convert from cents to dollars
    const costInDollars = cost / 100;
    return `${costInDollars.toFixed(2)}`;
  };

  const getStatusText = (deployment: any) => {
    if (deployment.deleted_at) {
      return 'Deleted';
    }
    if (deployment.status === 'CREATING') {
      return 'Creating...';
    }
    if (deployment.status === 'PROVISIONING') {
      return 'Provisioning...';
    }
    if (deployment.status === 'FAILED') {
      return 'Failed';
    }
    return 'Active';
  };

  const getAccessUrl = (deployment: Deployment) => {
    if (!deployment.domain) return null;

    // For Pangolin package, use admin_subdomain.domain
    if (deployment.package === 'Pangolin' && deployment.admin_subdomain) {
      return `https://${deployment.admin_subdomain}.${deployment.domain}`;
    }

    // For Pangolin+ and Pangolin+AI packages, use static_page_subdomain.domain
    if ((deployment.package === 'Pangolin+' || deployment.package === 'Pangolin+AI') && deployment.static_page_subdomain) {
      return `https://${deployment.static_page_subdomain}.${deployment.domain}`;
    }

    // Fallback to just the domain
    return `https://${deployment.domain}`;
  };

  const filteredDeployments = deployments.filter(deployment => {
    if (!searchQuery) return true;
    
    const searchLower = searchQuery.toLowerCase();
    return (
      (deployment.client_name?.toLowerCase().includes(searchLower)) ||
      (deployment.package?.toLowerCase().includes(searchLower)) ||
      (deployment.cloud_provider?.toLowerCase().includes(searchLower)) ||
      (deployment.domain?.toLowerCase().includes(searchLower))
    );
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredDeployments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedDeployments = filteredDeployments.slice(startIndex, endIndex);

  // Reset to page 1 when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">Manage Resources</h1>
          <p className="text-sm text-muted-foreground mt-1">
            Create secure proxies to your private applications
          </p>
        </div>
        <Link to="/create-deployment">
          <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
            + Add Deployment
          </Button>
        </Link>
      </div>

      {/* Search Input */}
      <div>
        <Input
          type="text"
          placeholder="Search resources..."
          className="max-w-sm"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      )}

      {!isLoading && filteredDeployments.length === 0 && deployments.length === 0 ? (
        <div className="border rounded-lg p-12 text-center bg-card">
          <h3 className="text-lg font-medium text-foreground mb-2">No deployments yet</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Get started by creating your first deployment
          </p>
          <Link to="/create-deployment">
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              Create Your First Deployment
            </Button>
          </Link>
        </div>
      ) : !isLoading && filteredDeployments.length === 0 && deployments.length > 0 ? (
        <div className="border rounded-lg p-12 text-center bg-card">
          <h3 className="text-lg font-medium text-foreground mb-2">No results found</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Try adjusting your search terms
          </p>
        </div>
      ) : (
        <>
          <div className="border rounded-lg bg-card ">
            <Table>
              <TableHeader>
                <TableRow className="border-b hover:bg-transparent">
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Name</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Package</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Cloud Provider</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Access</TableHead>
                  {user?.is_admin && (
                    <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Owner</TableHead>
                  )}
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Status</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Created</TableHead>
                  <TableHead className="h-12 px-4">
                    <span className="sr-only">Actions</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedDeployments.map((deployment) => (
                  <TableRow key={deployment.id} className="border-b hover:bg-muted/50">
                    <TableCell className="h-12 px-4 py-3 text-sm font-medium text-foreground">
                      {deployment.client_name || `deployment-${deployment.id}`}
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm text-foreground">
                      <span className="capitalize">{deployment.package || 'Pangolin'}</span>
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm text-foreground">
                      {deployment.cloud_provider || 'BYOVPS'}
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm">
                      {(() => {
                        const accessUrl = getAccessUrl(deployment);
                        return accessUrl ? (
                          <a
                            href={accessUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline text-sm flex items-center gap-1"
                          >
                            {accessUrl.replace('https://', '')}
                            <ExternalLink className="h-3 w-3" data-testid="external-link-icon" />
                          </a>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        );
                      })()}
                    </TableCell>
                    {user?.is_admin && (
                      <TableCell className="h-12 px-4 py-3 text-sm text-muted-foreground">
                        User ID: {deployment.user_id}
                      </TableCell>
                    )}
                    <TableCell className="h-12 px-4 py-3 text-sm">
                      <div className="flex items-center gap-2">
                        <div className={`h-2 w-2 rounded-full ${
                          !deployment.deleted_at ? 'bg-green-500' : 'bg-muted-foreground'
                        }`}></div>
                        <span className={`text-sm font-medium ${
                          !deployment.deleted_at ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'
                        }`}>
                          {!deployment.deleted_at ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm text-muted-foreground">
                      {new Date(deployment.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3">
                      <div className="flex items-center justify-end gap-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {!deployment.deleted_at && (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleDeleteDeployment(deployment)
                                }
                                disabled={deletingId === deployment.id}
                                className="text-destructive focus:text-destructive"
                              >
                                {deletingId === deployment.id
                                  ? 'Deactivating...'
                                  : 'Deactivate'}
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="text-sm h-8"
                          onClick={() => setDetailOpen(deployment.id)}
                        >
                          Details
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {/* Pagination */}
          {filteredDeployments.length > 0 && (
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <select 
                  className="h-8 rounded-md border bg-background px-3 py-1 text-sm"
                  value={itemsPerPage}
                  onChange={(e) => {
                    setItemsPerPage(Number(e.target.value));
                    setCurrentPage(1);
                  }}
                >
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
              <div className="flex items-center gap-4">
                <span>Page {currentPage} of {totalPages}</span>
                <div className="flex items-center gap-1">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-8 w-8 p-0"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(1)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 17l-5-5 5-5" />
                      <path d="M5 17l5-5-5-5" />
                    </svg>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-8 w-8 p-0"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(currentPage - 1)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M15 18l-6-6 6-6" />
                    </svg>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-8 w-8 p-0"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(currentPage + 1)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M9 18l6-6-6-6" />
                    </svg>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-8 w-8 p-0"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(totalPages)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M13 17l5-5-5-5" />
                      <path d="M19 17l-5-5 5-5" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {detailOpen && (
        <Modal
          open={true}
          onClose={() => setDetailOpen(null)}
          title={(() => {
            const d = deployments.find(x => x.id === detailOpen);
            return `${d?.client_name || `Deployment ${detailOpen}`} Details`;
          })()}
        >
          {(() => {
            const d = deployments.find(x => x.id === detailOpen);
            if (!d) return null;            
            return (
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-foreground mb-3">General Information</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Name</span>
                        <span className="text-sm font-medium">{d.client_name || `deployment-${d.id}`}</span>
                      </div>
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Package</span>
                        <span className="text-sm font-medium capitalize">{d.package || 'Pangolin'}</span>
                      </div>
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Status</span>
                        <div className="flex items-center gap-2">
                          <div className={`h-2 w-2 rounded-full ${
                            !d.deleted_at ? 'bg-green-500' : 'bg-gray-400'
                          }`}></div>
                          <span className={`text-sm font-medium ${
                            !d.deleted_at ? 'text-green-600' : 'text-gray-500'
                          }`}>
                            {!d.deleted_at ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Created</span>
                        <span className="text-sm font-medium">{new Date(d.created_at).toLocaleString()}</span>
                      </div>
                      {d.domain && (
                        <div className="flex justify-between py-1">
                          <span className="text-sm text-muted-foreground">Domain</span>
                          <span className="text-sm font-medium">{d.domain}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {(d.cloud_provider || d.region || d.instance_type) && (
                    <div>
                      <h4 className="text-sm font-medium text-foreground mb-3">Infrastructure</h4>
                      <div className="space-y-2">
                        {d.cloud_provider && (
                          <div className="flex justify-between py-1">
                            <span className="text-sm text-muted-foreground">Cloud Provider</span>
                            <span className="text-sm font-medium">{d.cloud_provider}</span>
                          </div>
                        )}
                        {d.region && (
                          <div className="flex justify-between py-1">
                            <span className="text-sm text-muted-foreground">Region</span>
                            <span className="text-sm font-medium">{d.region}</span>
                          </div>
                        )}
                        {d.instance_type && (
                          <div className="flex justify-between py-1">
                            <span className="text-sm text-muted-foreground">Instance Type</span>
                            <span className="text-sm font-medium">{d.instance_type}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <h4 className="text-sm font-medium text-foreground mb-3">Network</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">IP Address</span>
                        <span className="text-sm font-medium font-mono">
                          {d.vps_ip_address || d.komodo_host_ip || d.instance_ip ||
                            (d.status === 'CREATING' ? 'Creating...' :
                             d.status === 'PROVISIONING' ? 'Provisioning...' :
                             d.status === 'FAILED' ? 'Failed' : 'Provisioning...')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })()}
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Deployment"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Are you sure you want to delete the deployment "{deploymentToDelete?.client_name || `deployment-${deploymentToDelete?.id}`}"?
          </p>
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-700 font-medium">
              ⚠️ Warning: This action cannot be undone and will destroy all associated infrastructure.
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteDeployment}
              disabled={deletingId === deploymentToDelete?.id}
            >
              {deletingId === deploymentToDelete?.id ? 'Deleting...' : 'Delete Deployment'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Dashboard;
