#!/usr/bin/env python3
"""
Test automatic deployment termination by forcing a billing scenario
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import psycopg2
from datetime import datetime, timezone, timedelta
from app.services.billing_job import run_billing_cycle

# Database connection details
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'manidae',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def get_user_balance(user_id=1):
    """Get current user balance"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT balance FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            return float(result[0]) if result else 0.0
    finally:
        conn.close()

def set_user_balance(user_id, balance):
    """Set user balance to specific amount"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("UPDATE users SET balance = %s WHERE id = %s", (balance, user_id))
            conn.commit()
            print(f"✓ Set user {user_id} balance to €{balance:.2f}")
    finally:
        conn.close()

def reset_deployment_billing(deployment_id):
    """Reset deployment billing to force new billing"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            # Set last_billed_at to yesterday to force billing for today
            yesterday = datetime.now(timezone.utc) - timedelta(days=1)
            yesterday_end = yesterday.replace(hour=23, minute=59, second=59)
            
            cur.execute("""
                UPDATE deployments 
                SET last_billed_at = %s 
                WHERE id = %s
            """, (yesterday_end, deployment_id))
            conn.commit()
            print(f"✓ Reset deployment {deployment_id} billing state to force new charge")
    finally:
        conn.close()

def get_deployment_status(deployment_id):
    """Get deployment status"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, status, deleted_at, last_billed_at
                FROM deployments 
                WHERE id = %s
            """, (deployment_id,))
            return cur.fetchone()
    finally:
        conn.close()

def get_recent_transactions(user_id=1, limit=5):
    """Get recent transactions for user"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, amount, type, description, created_at
                FROM transactions 
                WHERE user_id = %s 
                ORDER BY created_at DESC 
                LIMIT %s
            """, (user_id, limit))
            return cur.fetchall()
    finally:
        conn.close()

def test_forced_termination():
    """Test automatic termination by forcing a billing scenario"""
    
    print("🚨 Testing FORCED Automatic Deployment Termination")
    print("=" * 55)
    
    user_id = 1
    deployment_id = 4
    
    # Step 1: Check initial state
    print("\n📋 Step 1: Check initial state")
    initial_balance = get_user_balance(user_id)
    deployment_status = get_deployment_status(deployment_id)
    
    print(f"Initial user balance: €{initial_balance:.2f}")
    if deployment_status:
        dep_id, status, deleted_at, last_billed_at = deployment_status
        print(f"Deployment {dep_id}: {status}")
        print(f"Last billed at: {last_billed_at}")
    else:
        print("❌ No deployment found!")
        return
    
    # Step 2: Set balance to very low amount
    print("\n📋 Step 2: Set balance to very low amount")
    low_balance = 0.50  # Much less than daily cost of €1.92
    set_user_balance(user_id, low_balance)
    
    # Step 3: Reset deployment billing to force a charge
    print("\n📋 Step 3: Reset deployment billing to force new charge")
    reset_deployment_billing(deployment_id)
    
    # Step 4: Run billing cycle
    print("\n📋 Step 4: Run billing cycle to trigger termination")
    print("This should charge €1.92, making balance negative...")
    
    result = run_billing_cycle()
    print(f"Billing result: {result}")
    
    # Step 5: Check results
    print("\n📋 Step 5: Check results")
    
    # Check new balance
    new_balance = get_user_balance(user_id)
    print(f"New user balance: €{new_balance:.2f}")
    
    # Check deployment status
    new_deployment_status = get_deployment_status(deployment_id)
    if new_deployment_status:
        dep_id, status, deleted_at, last_billed_at = new_deployment_status
        print(f"Deployment {dep_id}: {status}")
        print(f"Deleted at: {deleted_at}")
    
    # Check recent transactions
    recent_transactions = get_recent_transactions(user_id, 3)
    print(f"\nRecent transactions:")
    for tx in recent_transactions:
        tx_id, amount, tx_type, description, created_at = tx
        print(f"  {created_at.strftime('%H:%M:%S')} | €{float(amount):6.2f} | {tx_type:12} | {description}")
    
    # Step 6: Analyze results
    print("\n📋 Step 6: Final Analysis")
    print("-" * 40)
    
    success_indicators = []
    
    if new_balance < 0:
        success_indicators.append("✅ Balance went negative")
        print(f"✅ Balance went negative: €{low_balance:.2f} → €{new_balance:.2f}")
    else:
        print(f"❌ Balance did not go negative: €{new_balance:.2f}")
    
    if result['charged_users'] > 0:
        success_indicators.append("✅ Billing charge applied")
        print(f"✅ Billing charge applied: {result['charged_users']} user(s) charged")
    else:
        print(f"❌ No billing charges applied")
    
    if result['terminated_deployments'] > 0:
        success_indicators.append("✅ Deployments terminated")
        print(f"✅ Deployments terminated: {result['terminated_deployments']} deployment(s)")
    else:
        print(f"❌ No deployments terminated")
    
    # Check for termination transaction
    termination_transactions = [tx for tx in recent_transactions if tx[2] == 'termination']
    if termination_transactions:
        success_indicators.append("✅ Termination transaction recorded")
        print(f"✅ Termination transaction recorded")
        for tx in termination_transactions:
            tx_id, amount, tx_type, description, created_at = tx
            print(f"   → {description}")
    else:
        print(f"❌ No termination transaction found")
    
    # Check deployment status
    if new_deployment_status and new_deployment_status[1] in ['DELETED', 'DESTROYED']:
        success_indicators.append("✅ Deployment marked as terminated")
        print(f"✅ Deployment marked as {new_deployment_status[1]}")
    else:
        print(f"❌ Deployment not marked as terminated")
    
    if result['errors']:
        print(f"❌ Errors occurred: {result['errors']}")
    else:
        success_indicators.append("✅ No errors")
        print(f"✅ No errors during process")
    
    print(f"\n🎯 FINAL CONCLUSION:")
    if len(success_indicators) >= 4:  # Most indicators successful
        print("✅ AUTOMATIC TERMINATION WORKS CORRECTLY!")
        print("   The system properly:")
        for indicator in success_indicators:
            print(f"   {indicator}")
    else:
        print("❌ AUTOMATIC TERMINATION HAS ISSUES!")
        print(f"   Only {len(success_indicators)} out of 6 success indicators met")
        print("   This suggests the termination logic needs investigation")

if __name__ == "__main__":
    test_forced_termination()
