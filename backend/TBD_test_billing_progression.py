#!/usr/bin/env python3
"""
Test script to simulate billing progression over multiple days
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone, timedelta
from app.services.billing_job import run_billing_cycle

def test_billing_progression():
    """Test billing over multiple simulated days"""
    
    print("=== Testing Billing Progression ===")
    
    # Test Day 1 (today) - should not charge (already billed)
    print("\n--- Day 1 (Today): Should not charge ---")
    today = datetime.now(timezone.utc)
    result = run_billing_cycle(today)
    print(f"Result: {result}")
    
    # Test Day 2 (tomorrow) - should charge for next day
    print("\n--- Day 2 (Tomorrow): Should charge for next day ---")
    tomorrow = today + timedelta(days=1)
    result = run_billing_cycle(tomorrow)
    print(f"Result: {result}")
    
    # Test Day 3 (day after tomorrow) - should charge for another day
    print("\n--- Day 3 (Day after tomorrow): Should charge for another day ---")
    day_after_tomorrow = today + timedelta(days=2)
    result = run_billing_cycle(day_after_tomorrow)
    print(f"Result: {result}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_billing_progression()
