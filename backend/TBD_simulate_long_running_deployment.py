#!/usr/bin/env python3
"""
Simulate a long-running deployment with hourly billing job runs
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import psycopg2
from datetime import datetime, timezone, timedelta
from app.services.billing_job import run_billing_cycle

# Database connection details
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'manidae',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def get_deployment_info(deployment_id=4):
    """Get current deployment information"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, created_at, last_billed_at, status, cost 
                FROM deployments 
                WHERE id = %s
            """, (deployment_id,))
            result = cur.fetchone()
            if result:
                return {
                    'id': result[0],
                    'created_at': result[1],
                    'last_billed_at': result[2],
                    'status': result[3],
                    'cost': result[4]
                }
            return None
    finally:
        conn.close()

def get_user_balance(user_id=1):
    """Get current user balance"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT balance FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            return float(result[0]) if result else 0.0
    finally:
        conn.close()

def get_billing_transactions(deployment_id=4):
    """Get billing transactions for deployment"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, amount, description, created_at 
                FROM transactions 
                WHERE type = 'billing' AND description LIKE %s
                ORDER BY created_at
            """, (f'%deployment #{deployment_id}%',))
            return cur.fetchall()
    finally:
        conn.close()

def simulate_time_passing(deployment_id, target_time):
    """Simulate time passing by updating deployment created_at"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            # Update the deployment's created_at to simulate it being older
            cur.execute("""
                UPDATE deployments 
                SET created_at = %s 
                WHERE id = %s
            """, (target_time, deployment_id))
            conn.commit()
            print(f"✓ Updated deployment {deployment_id} created_at to {target_time}")
    finally:
        conn.close()

def reset_billing_state(deployment_id):
    """Reset billing state for clean simulation"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            # Reset last_billed_at
            cur.execute("""
                UPDATE deployments 
                SET last_billed_at = NULL 
                WHERE id = %s
            """, (deployment_id,))
            
            # Delete existing billing transactions for this deployment
            cur.execute("""
                DELETE FROM transactions 
                WHERE type = 'billing' AND description LIKE %s
            """, (f'%deployment #{deployment_id}%',))
            
            # Reset user balance to a high amount
            cur.execute("""
                UPDATE users 
                SET balance = 1000.00 
                WHERE id = 1
            """)
            
            conn.commit()
            print(f"✓ Reset billing state for deployment {deployment_id}")
    finally:
        conn.close()

def run_simulation():
    """Run the complete simulation"""
    deployment_id = 4
    
    print("🚀 Starting Long-Running Deployment Billing Simulation")
    print("=" * 60)
    
    # Step 1: Reset billing state
    print("\n📋 Step 1: Reset billing state")
    reset_billing_state(deployment_id)
    
    # Step 2: Set deployment to be 5 days old
    print("\n📋 Step 2: Simulate deployment created 5 days ago")
    five_days_ago = datetime.now(timezone.utc) - timedelta(days=5)
    simulate_time_passing(deployment_id, five_days_ago)
    
    # Step 3: Show initial state
    print("\n📋 Step 3: Initial deployment state")
    deployment = get_deployment_info(deployment_id)
    balance = get_user_balance()
    print(f"Deployment ID: {deployment['id']}")
    print(f"Created at: {deployment['created_at']}")
    print(f"Last billed at: {deployment['last_billed_at']}")
    print(f"Status: {deployment['status']}")
    print(f"Cost: €{deployment['cost']/100:.2f}/hour = €{deployment['cost']*24/100:.2f}/day")
    print(f"User balance: €{balance:.2f}")
    
    # Step 4: Simulate billing job running every hour for multiple days
    print("\n📋 Step 4: Simulate billing job running every hour")
    print("-" * 40)
    
    # Start from 5 days ago and run billing every hour until now
    current_time = five_days_ago
    now = datetime.now(timezone.utc)
    hour_count = 0
    
    while current_time <= now:
        hour_count += 1
        print(f"\n⏰ Hour {hour_count}: {current_time.strftime('%Y-%m-%d %H:00')} UTC")
        
        # Run billing cycle for this time
        result = run_billing_cycle(current_time)
        
        if result['charged_users'] > 0:
            print(f"   💰 Charged {result['charged_users']} user(s)")
            # Get updated balance
            balance = get_user_balance()
            print(f"   💳 New balance: €{balance:.2f}")
        else:
            print(f"   ⏭️  No charges (already billed for current period)")
        
        if result['terminated_deployments'] > 0:
            print(f"   🛑 Terminated {result['terminated_deployments']} deployment(s)")
            break
            
        if result['errors']:
            print(f"   ❌ Errors: {result['errors']}")
        
        # Move to next hour
        current_time += timedelta(hours=1)
        
        # Limit output for readability - show every 6 hours after first day
        if hour_count > 24 and hour_count % 6 != 0:
            continue
    
    # Step 5: Show final results
    print("\n📋 Step 5: Final Results")
    print("-" * 40)
    
    # Show final deployment state
    deployment = get_deployment_info(deployment_id)
    balance = get_user_balance()
    print(f"Final deployment state:")
    print(f"  Last billed at: {deployment['last_billed_at']}")
    print(f"  Status: {deployment['status']}")
    print(f"Final user balance: €{balance:.2f}")
    
    # Show all billing transactions
    transactions = get_billing_transactions(deployment_id)
    print(f"\nBilling transactions ({len(transactions)} total):")
    for tx in transactions:
        tx_id, amount, description, created_at = tx
        print(f"  {created_at.strftime('%Y-%m-%d %H:%M')} | €{float(amount):6.2f} | {description}")
    
    # Calculate expected vs actual charges
    days_running = (now - five_days_ago).days + 1  # +1 for partial day
    daily_cost = deployment['cost'] * 24 / 100  # Convert cents/hour to euros/day
    expected_charges = days_running * daily_cost
    actual_charges = sum(abs(float(tx[1])) for tx in transactions)
    
    print(f"\n📊 Billing Analysis:")
    print(f"  Days running: {days_running}")
    print(f"  Daily cost: €{daily_cost:.2f}")
    print(f"  Expected total charges: €{expected_charges:.2f}")
    print(f"  Actual total charges: €{actual_charges:.2f}")
    print(f"  Difference: €{abs(expected_charges - actual_charges):.2f}")
    
    if abs(expected_charges - actual_charges) < 0.01:
        print("  ✅ Billing is accurate!")
    else:
        print("  ❌ Billing discrepancy detected!")
    
    print("\n🎉 Simulation Complete!")

if __name__ == "__main__":
    run_simulation()
